use std::collections::{
    HashMap,
    HashSet,
};
use dashmap::DashMap;
use std::fs::{
    File,
    create_dir_all,
    remove_dir_all,
    remove_file,
};
use std::io::{
    <PERSON>ufRead,
    BufReader,
    Write,
};
use std::path::Path;
use std::process::Command;
use std::thread;
use std::time::Instant;

use clap::Parser as ClapParser;
use mysql::{
    OptsBuilder,
    Pool,
    prelude::Queryable,
};
use rayon::iter::{
    IntoParallelRefIterator,
    ParallelIterator,
};
use serde_json;

use eterna::utils_classes::{
    MYSQLConfig,
    MYSQLValue,
    Parser,
    SnortConfig,
    SnortParser,
};

use eterna::utils::{
    create_name_of_database,
    create_name_of_index,
    create_path_of_infile,
    evenly_sized_batches,
    get_no_of_infiles,
};

use eterna::utils_parsers::{
    parse_ln,
    ConfigType,
};

// 8MB buffer for optimal I/O performance
const FILE_BUFFER_SIZE: usize = 8 * 1024 * 1024;

#[derive(ClapParser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long = "source-log")]
    source_log: String,
    // /FOO/BAR/BAZ/2025-06-13--Fri.log

    #[arg(long = "log-date")]
    log_date: String,
    // 2020-01-02

    #[arg(long = "already-accomplished", num_args = 0..)]
    already_accomplished: Vec<String>,
    // [] OR ["Sensor-1", "Sensor-2", ...]

    #[arg(long = "sensor-list-of-names", num_args = 1..)]
    sensor_list_of_names: Vec<String>,
    // ["Sensor-1", "Sensor-2", "Sensor-3", ...]

    #[arg(long = "sensor-list-of-names-and-addresses", num_args = 1..)]
    sensor_list_of_names_and_addresses: Vec<String>,
    // ["Sensor-1", "***********", "Sensor-2", "***********", ...]

    #[arg(long = "sensor-dict-of-addresses-and-names")]
    sensor_dict_of_addresses_and_names: String,
    // "{\"***********\": \"Sensor-1\", \"***********\": \"Sensor-2\", ...}"

    #[arg(long = "force", num_args = 0)]
    #[arg(default_value_t = false)]
    force: bool,
    // true/false
}

fn trim_newlines(line: &mut String) {
    while line.ends_with('\n') || line.ends_with('\r') {
        line.pop();
    }
}

fn main() {
    let start = Instant::now();
    let args = Args::parse();

    // println!("Source log: {:?}", args.source_log);
    // println!("Log date: {:?}", args.log_date);
    // println!("Already accomplished: {:?}", args.already_accomplished);
    // println!("Sensor list of names: {:?}", args.sensor_list_of_names);
    // println!("Sensor list of names and addresses: {:?}", args.sensor_list_of_names_and_addresses);
    // println!("Sensor dict of addresses and names: {:?}", args.sensor_dict_of_addresses_and_names);

    // string -> dict
    let sensor_dict_of_addresses_and_names: HashMap<String, String> =
        serde_json::from_str(&args.sensor_dict_of_addresses_and_names)
            .expect("Failed to parse sensor_dict_of_addresses_and_names");

    // list -> set for O(1) lookup
    let already_accomplished: HashSet<String> =
        args.already_accomplished.into_iter().collect();

    // create dictionary of instances
    let sensor_names_and_instances: DashMap<String, SnortParser> = DashMap::new();
    for s_n in &args.sensor_list_of_names {
        sensor_names_and_instances.insert(
            s_n.clone(),
            SnortParser::new(
                SnortConfig::SLUG.value_string(),
                args.log_date.to_string(),
                s_n.to_string(),
            ),
        );
    }

    // __PARSING__ start

    // open file with larger buffer for better I/O performance
    let file = File::open(&args.source_log)
        .expect(&format!("Failed to open source log: {}", args.source_log));
    let mut reader = BufReader::with_capacity(FILE_BUFFER_SIZE, file);

    println!("parsing...");

    // process file in chunks to avoid loading entire file into memory
    let pool_chunksize = if let eterna::utils_classes::MYSQLValue::Int(size) = MYSQLConfig::POOL_CHUNKSIZE.value() {
        size
    } else {
        panic!("Error getting pool_chunksize from MYSQLConfig");
    };

    loop {
        let mut chunk = Vec::with_capacity(pool_chunksize);

        // read chunk of lines
        for _ in 0..pool_chunksize {
            let mut line = String::new();
            match reader.read_line(&mut line) {
                Ok(0) => break, // EOF
                Ok(_) => {
                    // remove newline character
                    trim_newlines(&mut line);
                    chunk.push(line);
                }
                Err(e) => panic!("Error reading line: {}", e),
            }
        }

        if chunk.is_empty() {
            // EOF reached
            break;
        }

        // process chunk in parallel, aggregate locally to avoid DashMap contention
        let local_results: HashMap<String, Vec<Vec<String>>> = chunk
            .par_iter()
            .map(|line| {
                let (sensor_name, parsed_ln) = parse_ln(
                    line.trim(),
                    ConfigType::Snort,
                    &args.sensor_list_of_names_and_addresses,
                    &sensor_dict_of_addresses_and_names,
                );

                // check if sensor is already accomplished (O(1) lookup)
                if let Some(ref name) = sensor_name {
                    if already_accomplished.contains(name) {
                        return None;
                    }
                }

                match (sensor_name, parsed_ln) {
                    (Some(name), Some(row)) => Some((name, row)),
                    _ => None,
                }
            })
            .filter_map(|x| x)
            .fold(HashMap::new, |mut acc, (name, row)| {
                acc.entry(name).or_insert_with(Vec::new).push(row);
                acc
            })
            .reduce(HashMap::new, |mut acc1, acc2| {
                for (k, mut v) in acc2 {
                    acc1.entry(k).or_insert_with(Vec::new).append(&mut v);
                }
                acc1
            });

        // collect results into sensor instances
        for (name, rows) in local_results {
            if let Some(mut instance) = sensor_names_and_instances.get_mut(&name) {
                instance.rows.extend(rows);
            }
        }

        // println!("Processed {} lines in {:?}", chunk.len(), start.elapsed());
    }

    // __TODO__ temporary
    println!("\nLines parsed per sensor:");
    for entry in sensor_names_and_instances.iter() {
        println!("  {}: {} lines", entry.key(), entry.value().rows.len());
    }

    // __PARSING__ end

    // __DB_HANDLING__ start

    for entry in sensor_names_and_instances.iter() {
        let sensor_name = entry.key();
        let instance = entry.value();

        let dest_dir          = format!("{}/{}/{}", SnortConfig::get_logs_parsed_dir(), sensor_name, args.log_date);
        let accomplished_file = format!("{}/{}-accomplished.log", dest_dir, args.log_date);
        let log_file          = format!("{}/{}.log", dest_dir, args.log_date);

        let database_name = create_name_of_database(&SnortConfig::SLUG.value_string(), &args.log_date, sensor_name);

        // ################################################

        // remove and/or create dest_dir
        if Path::new(&dest_dir).exists() {
            let mut should_rm_dest_dir = false;

            if args.force {
                should_rm_dest_dir = true;
            } else {
                if Path::new(&accomplished_file).exists() {
                    println!("{} for sensor {} is already parsed. skipping", args.log_date, sensor_name);
                    continue;
                } else {
                    should_rm_dest_dir = true;
                }
            };

            if should_rm_dest_dir {
                println!("removing {}", dest_dir);
                if let Err(e) = remove_dir_all(&dest_dir) {
                    eprintln!("Error removing directory {}: {}", dest_dir, e);
                }
                println!("creating {}", dest_dir);
                if let Err(e) = create_dir_all(&dest_dir) {
                    eprintln!("Error creating directory {}: {}", dest_dir, e);
                }
            }
        } else {
            println!("creating {}", dest_dir);
            if let Err(e) = create_dir_all(&dest_dir) {
                eprintln!("Error creating directory {}: {}", dest_dir, e);
            }
        }

        // ################################################

        // START __inserting_into_dbs__

        let mysql_host = match MYSQLConfig::MYSQL_HOST.value() {
            MYSQLValue::Str(host) => host,
            _ => panic!("Error getting MYSQL_HOST"),
        };
        let mysql_user = match MYSQLConfig::MYSQL_MASTER.value() {
            MYSQLValue::Str(user) => user,
            _ => panic!("Error getting MYSQL_MASTER"),
        };
        let mysql_password = match MYSQLConfig::MYSQL_MASTER_PASSWD.value() {
            MYSQLValue::Str(password) => password,
            _ => panic!("Error getting MYSQL_MASTER_PASSWD"),
        };

        let db_opts = OptsBuilder::new()
            .ip_or_hostname(Some(mysql_host.clone()))
            .user(Some(mysql_user.clone()))
            .pass(Some(mysql_password.clone()));

        // drop/create database
        match Pool::new(db_opts) {
            Ok(pool) => {
                match pool.get_conn() {
                    Ok(mut conn) => {
                        println!("dropping database {}", database_name);
                        if let Err(e) = conn.query_drop(format!("DROP DATABASE IF EXISTS {};", database_name)) {
                            eprintln!("Error dropping database {}: {}", database_name, e);
                        }

                        println!("creating database {}", database_name);
                        if let Err(e) = conn.query_drop(format!("CREATE DATABASE {};", database_name)) {
                            eprintln!("Error creating database {}: {}", database_name, e);
                        }
                    }
                    Err(e) => {
                        eprintln!("Error getting database connection: {}", e);
                    }
                }
            }
            Err(e) => {
                eprintln!("Error creating database pool: {}", e);
            }
        }

        // ################################################

        let no_of_infiles = get_no_of_infiles(instance.no_of_rows());

        if no_of_infiles > 0 {
            println!("{} rows will be inserted into database", instance.no_of_rows());

            // Create table and insert data
            let db_opts_with_db = OptsBuilder::new()
                .ip_or_hostname(Some(mysql_host.clone()))
                .user(Some(mysql_user.clone()))
                .pass(Some(mysql_password.clone()))
                .db_name(Some(database_name.clone()));

            match Pool::new(db_opts_with_db) {
                Ok(pool) => {
                    match pool.get_conn() {
                        Ok(mut conn) => {
                            // Create table
                            let table_name = SnortConfig::get_table_name();
                            println!("creating table {}", table_name);

                            let db_columns = match SnortConfig::DB_COLUMNS.value() {
                                MYSQLValue::Str(cols) => cols,
                                _ => panic!("Error getting DB_COLUMNS"),
                            };

                            if let Err(e) = conn.query_drop(format!("CREATE TABLE {} ({})", table_name, db_columns)) {
                                eprintln!("Error creating table {}: {}", table_name, e);
                                return;
                            }

                            println!("{} infiles will be created", no_of_infiles);

                            // Process infiles in batches
                            let batches = evenly_sized_batches(no_of_infiles as isize, None);

                            for (batch_index, batch) in batches.iter().enumerate() {
                                let batch_index = batch_index + 1;
                                println!("batch {}: writing into {} infiles", batch_index, batch.len());

                                let mut handles = vec![];
                                let mut infile_paths = vec![];

                                // Get infile chunksize
                                let infile_chunksize = match MYSQLConfig::INFILE_CHUNKSIZE.value() {
                                    MYSQLValue::Int(size) => size,
                                    _ => panic!("Error getting INFILE_CHUNKSIZE"),
                                };

                                // STEP 1: create n infiles at the same time (using threads)
                                for &infile_index in batch {
                                    let infile_path = create_path_of_infile(&database_name, &table_name, Some(infile_index));
                                    let start_of_chunk = infile_chunksize * (infile_index - 1);
                                    let end_of_chunk = start_of_chunk + infile_chunksize;

                                    infile_paths.push(infile_path.clone());

                                    // Clone necessary data for the thread
                                    let instance_rows = instance.rows.clone();
                                    let terminated_by = match MYSQLConfig::TERMINATED_BY.value() {
                                        MYSQLValue::Str(term) => term,
                                        _ => panic!("Error getting TERMINATED_BY"),
                                    };
                                    let enclosed_by = match MYSQLConfig::ENCLOSED_BY.value() {
                                        MYSQLValue::Str(enc) => enc,
                                        _ => panic!("Error getting ENCLOSED_BY"),
                                    };

                                    let handle = thread::spawn(move || {
                                        println!("  writing into {} ({}/{}): {} -> {}", infile_path, infile_index, no_of_infiles, start_of_chunk, end_of_chunk);

                                        let mut row_id = start_of_chunk;
                                        match std::fs::File::create(&infile_path) {
                                            Ok(mut file) => {
                                                for instance_row in instance_rows.iter().skip(start_of_chunk).take(end_of_chunk - start_of_chunk) {
                                                    row_id += 1;

                                                    // Create row with ID prepended: (row_id,) + instance_row
                                                    let mut full_row = vec![row_id.to_string()];
                                                    full_row.extend(instance_row.iter().cloned());

                                                    // Format each cell with enclosed_by and join with terminated_by
                                                    let formatted_row: Vec<String> = full_row.iter()
                                                        .map(|cell| format!("{}{}{}", enclosed_by, cell, enclosed_by))
                                                        .collect();

                                                    let line = format!("{}\n", formatted_row.join(&terminated_by));
                                                    if let Err(e) = file.write_all(line.as_bytes()) {
                                                        eprintln!("Error writing to file {}: {}", infile_path, e);
                                                    }
                                                }
                                            }
                                            Err(e) => {
                                                eprintln!("Error creating file {}: {}", infile_path, e);
                                            }
                                        }
                                    });

                                    handles.push(handle);
                                }

                                // Wait for all threads to complete
                                for handle in handles {
                                    if let Err(e) = handle.join() {
                                        eprintln!("Thread panicked: {:?}", e);
                                    }
                                }

                                // STEP 2: insert the n infiles into database one at a time
                                println!("batch {}: inserting into {} from {} infiles", batch_index, table_name, infile_paths.len());

                                for (_infile_idx, infile_path) in infile_paths.iter().enumerate() {

                                    if !Path::new(infile_path).exists() {
                                        continue;
                                    }

                                    println!("  inserting from {}", infile_path);

                                    if let Err(e) = conn.query_drop("SET UNIQUE_CHECKS=0") {
                                        eprintln!("Error setting UNIQUE_CHECKS: {}", e);
                                    }
                                    if let Err(e) = conn.query_drop("SET FOREIGN_KEY_CHECKS=0") {
                                        eprintln!("Error setting FOREIGN_KEY_CHECKS: {}", e);
                                    }
                                    if let Err(e) = conn.query_drop("START TRANSACTION") {
                                        eprintln!("Error starting transaction: {}", e);
                                    }

                                    let terminated_by = match MYSQLConfig::TERMINATED_BY.value() {
                                        MYSQLValue::Str(term) => term,
                                        _ => panic!("Error getting TERMINATED_BY"),
                                    };
                                    let enclosed_by = match MYSQLConfig::ENCLOSED_BY.value() {
                                        MYSQLValue::Str(enc) => enc,
                                        _ => panic!("Error getting ENCLOSED_BY"),
                                    };
                                    let db_keys = match SnortConfig::DB_KEYS.value() {
                                        MYSQLValue::Str(keys) => keys,
                                        _ => panic!("Error getting DB_KEYS"),
                                    };

                                    let infile_statement = format!(
                                        r#"{} "{}"
                                        INTO TABLE {}
                                        FIELDS TERMINATED BY "{}"
                                        ENCLOSED BY '{}'
                                        LINES TERMINATED BY "\n"
                                        (ID,{})"#,
                                        MYSQLConfig::get_infile_statement(),
                                        infile_path,
                                        table_name,
                                        terminated_by,
                                        enclosed_by,
                                        db_keys
                                    );

                                    // JUMP_1
                                    // if let Err(e) = conn.query_drop(infile_statement) {
                                    //     eprintln!("Error executing infile statement: {}", e);
                                    // }

                                    // NOTE using shell command instead of JUMP_1
                                    //      because mysql ≥ 25.0 (set in Cargo.toml)
                                    //      has dropped support for "LOAD DATA LOCAL INFILE" entirely
                                    let infile_status = Command::new("mysql")
                                        .args([
                                            "-u", &mysql_user,
                                            &format!("-p{}", mysql_password),

                                            // for using infiles located in /tmp.
                                            // no harm to be on development.
                                            // __TODO__ check if required in production
                                            "--local-infile=1",

                                            "-e", &infile_statement,
                                            &database_name,
                                        ])
                                        .status();
                                    //
                                    match infile_status {
                                        Ok(exit_status) => {
                                            if !exit_status.success() {
                                                eprintln!("Infile statement failed for: {}", infile_path);
                                            }
                                        }
                                        Err(e) => {
                                            eprintln!("Error executing infile statement: {}", e);
                                        }
                                    }
                                }

                                // Commit after processing all infiles in the batch
                                println!("  committing...");
                                if let Err(e) = conn.query_drop("COMMIT") {
                                    eprintln!("Error committing transaction: {}", e);
                                }

                                // Remove infiles
                                for infile_path in &infile_paths {
                                    println!("  removing {}", infile_path);
                                    if let Err(e) = remove_file(infile_path) {
                                        eprintln!("Error removing file {}: {}", infile_path, e);
                                    }
                                }
                            }

                            // Create indexes
                            let tablenames_and_keys = match SnortConfig::TABLENAMES_AND_KEYS_FOR_INDEX.value() {
                                MYSQLValue::Tuple(tuples) => tuples,
                                _ => panic!("Error getting TABLENAMES_AND_KEYS_FOR_INDEX"),
                            };

                            for (table_name_for_index, key) in tablenames_and_keys {
                                let key_str = match key {
                                    eterna::utils_classes::StrAndInt::Str(s) => s,
                                    eterna::utils_classes::StrAndInt::Int(i) => i.to_string(),
                                };

                                let index_name = create_name_of_index(&key_str);
                                println!("creating index {}", index_name);

                                let index_prefix_length = match MYSQLConfig::INDEX_PREFIX_LENGTH.value() {
                                    MYSQLValue::Int(len) => len,
                                    _ => panic!("Error getting INDEX_PREFIX_LENGTH"),
                                };
                                let index_type = match MYSQLConfig::INDEX_TYPE.value() {
                                    MYSQLValue::Str(typ) => typ,
                                    _ => panic!("Error getting INDEX_TYPE"),
                                };

                                let create_index_statement = format!(
                                    "CREATE INDEX {} ON {} ({}({})) USING {}",
                                    index_name,
                                    table_name_for_index,
                                    key_str,
                                    index_prefix_length,
                                    index_type
                                );

                                match conn.query_drop(&create_index_statement) {
                                    Ok(_) => {
                                        if let Err(e) = conn.query_drop("COMMIT") {
                                            eprintln!("Error committing index creation: {}", e);
                                        }
                                    }
                                    Err(e) => {
                                        eprintln!("Error creating index: {:?}", e);
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            eprintln!("Error getting database connection: {}", e);
                        }
                    }
                }
                Err(e) => {
                    eprintln!("Error creating database pool: {}", e);
                }
            }
        }

        // __DB_HANDLING__ end
    }










    let duration = start.elapsed();
    println!("\nParser Finished in {:.2} seconds", duration.as_secs_f64());
}
