from django.templatetags.static import static

from os import path, mkdir, uname
from pathlib import Path

import environ


PROJECT_DIR = Path(__file__).resolve().parent.parent

## ----------------------------------------

env = environ.Env()
environ.Env.read_env(f'{PROJECT_DIR}/.env')

SECRET_ADMIN_PREFIX = env('SECRET_ADMIN_PREFIX')

TARCE_URL = env('TARCE_URL')

CAFEBAZAAR_URL = env('CAFEBAZAAR_URL')

SECRET_KEY = env('SECRET_KEY')

DEBUG = env.bool('DEBUG', default=False)

FIXTURES_DIR = f'{PROJECT_DIR}/{env("FIXTURES_DIR")}'

## logging
PROJECT_RUST_DIR       = f'{PROJECT_DIR}/{env("PROJECT_RUST_DIR")}'
PROJECT_LOGS_DIR       = f'{PROJECT_DIR}/{env("PROJECT_LOGS_DIR")}'
DJANGO_LOG_FILE        = f'{PROJECT_LOGS_DIR}/{env("DJANGO_LOG_FILE")}'
DJANGO_LOG_LEVEL       = env('DJANGO_LOG_LEVEL')
APACHE_ACCESS_LOG_FILE = f'{PROJECT_LOGS_DIR}/{env("APACHE_ACCESS_LOG_FILE")}'
APACHE_ERROR_LOG_FILE  = f'{PROJECT_LOGS_DIR}/{env("APACHE_ERROR_LOG_FILE")}'

PROJECT_TITLE_PERSIAN = env('PROJECT_TITLE_PERSIAN')
PROJECT_TITLE         = env('PROJECT_TITLE')
PROJECT_SLUG          = env('PROJECT_SLUG')
PROJECT_CREDIT        = env('PROJECT_CREDIT')
PROJECT_CREDIT_URL    = env('PROJECT_CREDIT_URL')
PROJECT_START_YEAR    = env('PROJECT_START_YEAR')

REDIS_PASSWORD = env('REDIS_PASSWORD')



## variables specific to this project

LOGS_DIR              = env('LOGS_DIR')
LOGS_PARSED_DIR       = env('LOGS_PARSED_DIR')
LOGS_LIVE_MONITOR_DIR = env('LOGS_LIVE_MONITOR_DIR')

LOGS_LIVE_SEND_DIR = env('LOGS_LIVE_SEND_DIR')

## __DISABLE_MAX_N_DAYS_BY_0__
MAX_N_DAYS = env.int('MAX_N_DAYS', default=0)

LIVE_SEND_HOST = env('LIVE_SEND_HOST')
LIVE_SEND_PORT = env.int('LIVE_SEND_PORT')

LIVE_PARSE_HOST = env('LIVE_PARSE_HOST')
LIVE_PARSE_PORT = env.int('LIVE_PARSE_PORT')

MYSQL_HOST          = env('MYSQL_HOST')
MYSQL_DATADIR       = env('MYSQL_DATADIR')
##
MYSQL_MASTER        = env('MYSQL_MASTER')
MYSQL_MASTER_PASSWD = env('MYSQL_MASTER_PASSWD')
##
MYSQL_R_USER        = env('MYSQL_R_USER')
MYSQL_R_USER_PASSWD = env('MYSQL_R_USER_PASSWD')

LOOKUP_URL              = env('LOOKUP_URL')
GEOLOCATION_URL__DOMAIN = env('GEOLOCATION_URL__DOMAIN')
GEOLOCATION_URL__IP     = env('GEOLOCATION_URL__IP')
IS_TOR_URL              = env('IS_TOR_URL')
TOR_PROXY               = env('TOR_PROXY')

DATABASES_STATISTICS_FILE    = f'{PROJECT_LOGS_DIR}/statistics-databases.json'
DISK_USAGE_STATISTICS_FILE   = f'{PROJECT_LOGS_DIR}/statistics-disk-usage.json'
LOGS_STATISTICS_FILE         = f'{PROJECT_LOGS_DIR}/statistics-logs.json'
PARSED_DATES_STATISTICS_FILE = f'{PROJECT_LOGS_DIR}/statistics-parsed-dates.json'

## ----------------------------------------

HOST_NAME = uname()[1]

## ----------------------------------------

if not DEBUG:
    ALLOWED_HOSTS = env('ALLOWED_HOSTS').split(',')
else:
    ALLOWED_HOSTS = [
        ## ----- django server in terminal -----

        ## browser -> server
        ## (django server listening at 127.0.0.1:8000)
        '127.0.0.1:8000',
        'localhost:8000',
        '127.0.0.1',
        'localhost',

        ## emulator -> server
        ## (django server listening at 127.0.0.1:8000)
        '********:8000',
        '********',

        ## phone (debug) -> server
        ## (django server listening at 0.0.0.0:8000)
        '*************:8000',
        '*************',

        ## ----- local service -----

        ## phone (production) -> local service
        ## (service listening at 0.0.0.0:8001)
        '*************:8001',
        '*************',

        ## browser -> local service
        ## (service listening at 0.0.0.0:8001)
        f'{PROJECT_SLUG}.local:8001',
        f'{PROJECT_SLUG}.local',
    ]

## ----------------------------------------

INSTALLED_APPS = [
    ## keep at the top
    ## so that runnig 'manage.py collectstatic'
    ## will run my customized version of collectstatic
    'django_rahavard.apps.DjangoRahavardConfig',

    ## JUMP_10 (keep above JUMP_6)
    'unfold',
    'unfold.contrib.forms',  ## to have WYSIWYG editor in admin panel
    'unfold.contrib.filters',

    'django.contrib.admin',  ## JUMP_6
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',  ## by me

    'accounts.apps.AccountsConfig',
    'base.apps.BaseConfig',
    'commands.apps.CommandsConfig',
    'daemon.apps.DaemonConfig',
    'dhcp.apps.DhcpConfig',
    'dns.apps.DnsConfig',
    'filterlog.apps.FilterLogConfig',
    'general.apps.GeneralConfig',
    'geolocation.apps.GeolocationConfig',
    'malicious.apps.MaliciousConfig',
    'router.apps.RouterConfig',
    'routerboard.apps.RouterboardConfig',
    'snort.apps.SnortConfig',
    'squid.apps.SquidConfig',
    'switch.apps.SwitchConfig',
    'useraudit.apps.UserauditConfig',
    'usernotice.apps.UsernoticeConfig',
    'userwarning.apps.UserwarningConfig',
    'vmware.apps.VmwareConfig',
    'vpnserver.apps.VpnserverConfig',
    'windowsserver.apps.WindowsserverConfig',

    'captcha',  ## JUMP_3
    'corsheaders',  ## JUMP_2
    'django_rahavard_robots.apps.DjangoRahavardRobotsConfig',  ## JUMP_9
]

## ----------------------------------------

MIDDLEWARE = [
    ## JUMP_2 at the top
    'corsheaders.middleware.CorsMiddleware',

    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',

    'whitenoise.middleware.WhiteNoiseMiddleware',
]

## ----------------------------------------

ROOT_URLCONF = 'heart.urls'

## ----------------------------------------

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            f'{PROJECT_DIR}/templates',
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',

                'base.views.globals',
            ],
        },
    },
]

## ----------------------------------------

WSGI_APPLICATION = 'heart.wsgi.application'

## ----------------------------------------

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': f'{PROJECT_DIR}/db.sqlite3',

        ## by me, to secure database connections
        'CONN_MAX_AGE': 60,  ## connection pooling
        'OPTIONS': {
            'timeout': 20,
        },
    }
}

## ----------------------------------------

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

## ----------------------------------------

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Tehran'

USE_I18N = True  ## JUMP_1

USE_TZ = False
    ## NOTE https://stackoverflow.com/questions/14074696/django-datetimefield-with-utc-offset
    ##      if set to True,  Django stores date and time information in UTC in the database
    ##      if set to False, Django stores date and time information using the TIME_ZONE we specified in JUMP_1

## ----------------------------------------

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

## ------------------------------------------------------------------------------------------------------------------------

CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379',
        'OPTIONS': {
            'DB': 0,
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'SOCKET_TIMEOUT': 5,
            'SOCKET_CONNECT_TIMEOUT': 5,
            'PASSWORD': REDIS_PASSWORD,

            ## as stated by official documents
            ## at https://pypi.org/project/django-redis/,
            ## we should use 'redis.connection._HiredisParser'
            ## instead of 'redis.connection.HiredisParser'
            ## (which was seen to cause errors)
            'PARSER_CLASS': 'redis.connection._HiredisParser',
        },
    }
}

## ----------------------------------------

## handle static files
STATIC_URL = '/static/'
STATIC_ROOT = f'{PROJECT_DIR}/staticfiles'
STATICFILES_DIRS = [
    f'{PROJECT_DIR}/static',
]

## handle public/protected files
PUBLIC_MEDIA_URL  = '/uploads/'
PUBLIC_MEDIA_ROOT = f'{PROJECT_DIR}/static/uploads'
#
PROTECTED_MEDIA_URL  = '/protected_uploads/'
PROTECTED_MEDIA_ROOT = f'{PROJECT_DIR}/protected_uploads'

if not path.exists(PUBLIC_MEDIA_ROOT):
    mkdir(PUBLIC_MEDIA_ROOT)

if not path.exists(PROTECTED_MEDIA_ROOT):
    mkdir(PROTECTED_MEDIA_ROOT)

## ----------------------------------------

## SESSION/COOKIE

## store sessions in cookies (https://docs.djangoproject.com/en/4.2/topics/http/sessions/)
SESSION_ENGINE = 'django.contrib.sessions.backends.signed_cookies'

SESSION_COOKIE_AGE = 14 * (24 * 60 * 60)  ## 14 days

## using 'same-origin' instead of None for better security when possible
SECURE_CROSS_ORIGIN_OPENER_POLICY = 'same-origin'

## Base security settings that should always be enabled
SESSION_COOKIE_HTTPONLY = True  ## to prevent access to the stored data from JavaScript
SESSION_COOKIE_PATH = '/'

if not DEBUG:
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_SECONDS = 31_536_000  ## one year (0 by default. the greater the better)
    # SECURE_SSL_HOST = 'www.example.com'
    SECURE_SSL_REDIRECT = True  ## redirect from http to https

    X_FRAME_OPTIONS = 'DENY'  ## recommended by python manage.py check --deploy
    SECURE_HSTS_PRELOAD = True  ## recommended by python manage.py check --deploy

    SECURE_REFERRER_POLICY = 'same-origin'

    CSRF_COOKIE_SAMESITE = 'Lax'     ## Changed from 'None' to 'Lax' for better security
    CSRF_COOKIE_SECURE = True        ## to avoid transmitting the CSRF cookie over HTTP accidentally
    SESSION_COOKIE_SAMESITE = 'Lax'  ## Changed from 'None' to 'Lax'
    SESSION_COOKIE_SECURE = True     ## to avoid transmitting the session cookie over HTTP accidentally
else:
    CSRF_COOKIE_SAMESITE = 'Lax'
    CSRF_COOKIE_SECURE = False  ## Allow HTTP in development
    SESSION_COOKIE_SAMESITE = 'Lax'
    SESSION_COOKIE_SECURE = False  ## Allow HTTP in development

## ----------------------------------------

## JUMP_2 https://stackoverflow.com/questions/71184316/django-cors-headers-does-not-allow-a-request-from-an-allowed-origin
##        (previously: https://stackoverflow.com/questions/61443195/cross-origin-request-blocked-django-project)
if not DEBUG:
    CORS_ORIGIN_ALLOW_ALL = False
    CORS_ALLOWED_ORIGINS = [
        LOOKUP_URL.rstrip('/'),  ## no trailing /
    ]
    CORS_ALLOW_METHODS = [
        # 'DELETE',
        'GET',
        # 'OPTIONS',
        # 'PATCH',
        # 'POST',
        # 'PUT'
    ]
    CORS_ALLOW_CREDENTIALS = False
    CORS_ALLOW_HEADERS = [
        'accept',
        'accept-encoding',
        'authorization',
        'content-type',
        'dnt',
        'origin',
        'user-agent',
        'x-csrftoken',
        'x-requested-with',
    ]
else:
    CORS_ORIGIN_ALLOW_ALL = True

## ----------------------------------------

## JUMP_3
CAPTCHA_CHALLENGE_FUNCT       = 'captcha.helpers.word_challenge'
CAPTCHA_WORDS_DICTIONARY      = f'{PROJECT_DIR}/captcha-words'
CAPTCHA_DICTIONARY_MIN_LENGTH = 4  ## default 0
CAPTCHA_DICTIONARY_MAX_LENGTH = 5  ## default 99

## ----------------------------------------

## REST_FRAMEWORK

## ----------------------------------------

## custom user model
## (https://docs.djangoproject.com/en/5.0/topics/auth/customizing/#substituting-a-custom-user-model)
AUTH_USER_MODEL = 'accounts.User'

## ----------------------------------------

## JUMP_9
ALLOW_GOOGLE_BOTS = False

## ----------------------------------------

## JUMP_10 (https://github.com/unfoldadmin/django-unfold)
UNFOLD = {
    # 'SITE_ICON': lambda request: static('icon.svg'),  ## both modes, optimise for 32px height
    'SITE_ICON': {
        'light': lambda request: static('files/project-logo/logo.ico'),
        'dark': lambda request: static('files/project-logo/logo.ico'),
    },

    # 'SITE_LOGO': lambda request: static('logo.svg'),  ## both modes, optimise for 32px height
    'SITE_LOGO': {
        'light': lambda request: static('files/project-logo/logo.png'),
        'dark': lambda request: static('files/project-logo/logo.png'),
    },

    'LOGIN': {
        'image': lambda request: static('files/admin-login/admin-login.jpg'),
    },

    # 'STYLES': [
    #     lambda request: static('css/style.css'),
    # ],
    # 'SCRIPTS': [
    #     lambda request: static('js/script.js'),
    # ],

    'SIDEBAR': {
        'show_search': True,  # Search in applications and models names
    }
}

## ----------------------------------------
## LOGGING (https://www.youtube.com/watch?v=Z7BOBn8B5qA)

if not path.exists(PROJECT_LOGS_DIR):
    mkdir(PROJECT_LOGS_DIR)

LOG_FORMATTERS = (
    {
        'simple': {
            'format': '{asctime:s} {levelname} {message}',
            'style': '{',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
        'verbose': {
            'format': '{asctime:s} {levelname} {threadName} {thread:d} {module} {filename} {lineno:d} {name} {funcName} {process:d} {message}',
            'style': '{',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
    },
)

LOG_HANDLERS = {
    'console__handler': {
        'class': 'logging.StreamHandler',
        'formatter': 'simple',
    },
    'log__handler': {
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': DJANGO_LOG_FILE,
        'mode': 'a',
        'encoding': 'utf-8',
        'formatter': 'verbose',
        'backupCount': 10,  ## max no. of backups
        'maxBytes': 1024 * 1024 * 20,  ## 20 MB
    },
}

LOGGERS = (
    {
        'django': {
            'handlers': [
                'console__handler',
            ],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': [
                'log__handler',
            ],
            'level': DJANGO_LOG_LEVEL,
            'propagate': False,
        },
    },
)

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': LOG_FORMATTERS[0],
    'handlers': LOG_HANDLERS,
    'loggers': LOGGERS[0],
}
